import React, { useState, useEffect, useCallback, useRef } from 'react';

const PianoTiles = () => {
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [tiles, setTiles] = useState([]);
  const [speed, setSpeed] = useState(2);
  const gameAreaRef = useRef(null);
  const animationRef = useRef(null);
  const lastTileRef = useRef(0);
  const tileIdRef = useRef(0);

  const COLUMNS = 4;
  const TILE_HEIGHT = 120;
  const SPAWN_INTERVAL = 1000; // milliseconds

  // Generate a new tile
  const generateTile = useCallback(() => {
    const column = Math.floor(Math.random() * COLUMNS);
    const newTile = {
      id: tileIdRef.current++,
      column,
      position: -TILE_HEIGHT,
      clicked: false
    };
    setTiles(prev => [...prev, newTile]);
  }, []);

  // Start the game
  const startGame = () => {
    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setTiles([]);
    setSpeed(2);
    tileIdRef.current = 0;
    lastTileRef.current = Date.now();
    generateTile();
  };

  // Handle tile click
  const handleTileClick = useCallback((tileId) => {
    setTiles(prev => prev.map(tile => 
      tile.id === tileId ? { ...tile, clicked: true } : tile
    ));
    setScore(prev => prev + 1);
  }, []);

  // Game loop
  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = () => {
      const currentTime = Date.now();
      
      setTiles(prev => {
        const updatedTiles = prev.map(tile => ({
          ...tile,
          position: tile.position + speed
        }));

        // Check for game over conditions
        const gameArea = gameAreaRef.current;
        const gameAreaHeight = gameArea ? gameArea.clientHeight : 600;
        
        for (const tile of updatedTiles) {
          if (!tile.clicked && tile.position > gameAreaHeight - TILE_HEIGHT) {
            setGameOver(true);
            return updatedTiles;
          }
        }

        // Remove tiles that are off screen or clicked
        const activeTiles = updatedTiles.filter(tile => 
          tile.position < gameAreaHeight + TILE_HEIGHT && !tile.clicked
        );

        return activeTiles;
      });

      // Generate new tiles
      if (currentTime - lastTileRef.current > SPAWN_INTERVAL / (1 + score * 0.01)) {
        generateTile();
        lastTileRef.current = currentTime;
      }

      // Increase speed gradually
      setSpeed(prev => Math.min(prev + 0.001, 8));

      animationRef.current = requestAnimationFrame(gameLoop);
    };

    animationRef.current = requestAnimationFrame(gameLoop);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameStarted, gameOver, score, speed, generateTile]);

  // Handle clicks on game area (miss detection)
  const handleGameAreaClick = (e) => {
    if (!gameStarted || gameOver) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Check if click was on a tile
    const clickedOnTile = tiles.some(tile => {
      const tileLeft = (tile.column * (100 / COLUMNS));
      const tileRight = tileLeft + (100 / COLUMNS);
      const tileTop = tile.position;
      const tileBottom = tile.position + TILE_HEIGHT;
      
      const xPercent = (x / rect.width) * 100;
      
      return !tile.clicked && 
             xPercent >= tileLeft && 
             xPercent <= tileRight && 
             y >= tileTop && 
             y <= tileBottom;
    });

    if (!clickedOnTile) {
      setGameOver(true);
    }
  };

  // Touch handling for mobile
  const handleTouch = (e) => {
    e.preventDefault();
    if (e.touches[0]) {
      handleGameAreaClick({
        currentTarget: e.currentTarget,
        clientX: e.touches[0].clientX,
        clientY: e.touches[0].clientY
      });
    }
  };

  return (
    <div className="w-full h-screen bg-gradient-to-b from-cyan-300 via-blue-400 to-purple-500 relative overflow-hidden">
      <div 
        ref={gameAreaRef}
        className="w-full h-full relative cursor-pointer touch-none"
        onClick={handleGameAreaClick}
        onTouchStart={handleTouch}
      >
        {/* Score at top center */}
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 z-20">
          <div className="text-white text-6xl font-bold drop-shadow-lg">
            {score}
          </div>
        </div>

        {/* Column dividers */}
        {[...Array(COLUMNS)].map((_, index) => (
          <div
            key={index}
            className="absolute top-0 bottom-0 border-r border-white border-opacity-20 last:border-r-0"
            style={{
              left: `${(index * 100) / COLUMNS}%`,
              width: `${100 / COLUMNS}%`
            }}
          />
        ))}

        {/* Tiles */}
        {tiles.map(tile => (
          <div
            key={tile.id}
            className={`absolute transition-colors duration-150 cursor-pointer ${
              tile.clicked 
                ? 'bg-gradient-to-b from-emerald-400 to-green-500' 
                : 'bg-gradient-to-b from-blue-500 to-blue-700 hover:from-blue-400 hover:to-blue-600'
            }`}
            style={{
              left: `${(tile.column * 100) / COLUMNS}%`,
              width: `${100 / COLUMNS}%`,
              top: `${tile.position}px`,
              height: `${TILE_HEIGHT}px`,
              transform: tile.clicked ? 'scale(0.95)' : 'scale(1)',
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!tile.clicked) {
                handleTileClick(tile.id);
              }
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              e.preventDefault();
              if (!tile.clicked) {
                handleTileClick(tile.id);
              }
            }}
          />
        ))}

        {/* Game Over Overlay */}
        {gameOver && (
          <div className="absolute inset-0 bg-black bg-opacity-80 flex flex-col items-center justify-center z-30">
            <div className="text-white text-center">
              <h2 className="text-4xl font-bold mb-6 text-red-400">Game Over!</h2>
              <p className="text-2xl mb-8">Final Score: {score}</p>
              <button
                onClick={startGame}
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-bold text-xl rounded-xl transition-colors duration-200"
              >
                Play Again
              </button>
            </div>
          </div>
        )}

        {/* Start Screen */}
        {!gameStarted && !gameOver && (
          <div className="absolute inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center z-30">
            <div className="text-white text-center bg-gray-800 rounded-lg p-8 border border-gray-600">
              <h2 className="text-3xl font-bold mb-8 text-gray-100">Piano Tiles</h2>
              <p className="text-lg mb-8 text-gray-300">
                Tap the gray tiles as they fall
              </p>
              <button
                onClick={startGame}
                className="px-10 py-5 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white font-bold text-xl rounded-xl transition-all duration-200 transform hover:scale-105 border border-gray-500"
              >
                Start Game
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PianoTiles;