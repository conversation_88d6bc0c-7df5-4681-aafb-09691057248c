* {
	margin: 0;
	padding: 0;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	font-family: '<PERSON><PERSON><PERSON>', sans-serif;
	font-size: calc(10px + 0.1vmin);
	color: white;
}

/* app wrapper */
.app {
	width: 100vw;
	height: 100vh;
	display: inline-flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: stretch;
	align-content: center;
}

.three-container-style-wrapper {
	position: absolute;
	z-index: 0;
  height: 100vh;
	width: 100vw;
	object-fit: cover;
}

.app-footer {
	position: relative;
	margin-bottom: 24px;
	font-size: 1.5rem;
	letter-spacing: 1px;
	text-align: center;
	font-style: italic;
	/* border: 1px white dashed; */
}
