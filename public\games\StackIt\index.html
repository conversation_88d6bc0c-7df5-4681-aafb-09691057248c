<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StackIt - 3D Stacking Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }

        .container {
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .game-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature h3 {
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }

        .status {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .status h3 {
            color: #ffc107;
            margin-bottom: 0.5rem;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🎮</div>
        <h1>StackIt</h1>
        <div class="description">
            A thrilling 3D stacking game built with React and Three.js. Test your precision and timing as you stack blocks to build the tallest tower possible!
        </div>

        <div class="status">
            <h3>⚠️ Game Status</h3>
            <p>This game is currently being integrated into the platform. The original React application requires some dependency updates to work with modern Node.js versions.</p>
        </div>

        <div class="game-info">
            <h2>🎯 Game Features</h2>
            <div class="features">
                <div class="feature">
                    <h3>🎨 3D Graphics</h3>
                    <p>Stunning WebGL graphics powered by Three.js</p>
                </div>
                <div class="feature">
                    <h3>🎵 Physics Engine</h3>
                    <p>Realistic physics simulation using Physijs</p>
                </div>
                <div class="feature">
                    <h3>🏆 Leaderboards</h3>
                    <p>Compete with other players for the highest score</p>
                </div>
                <div class="feature">
                    <h3>📱 Responsive</h3>
                    <p>Works on both desktop and mobile devices</p>
                </div>
            </div>
        </div>

        <div>
            <h3>🎮 How to Play</h3>
            <p>Click or tap to drop blocks and stack them as high as possible. Perfect timing is key to achieving the highest score!</p>
        </div>

        <div style="margin-top: 2rem;">
            <button class="btn" disabled>
                🚧 Coming Soon
            </button>
            <a href="/games" class="btn">
                ← Back to Games
            </a>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            
            // Add floating animation
            container.style.animation = 'float 6s ease-in-out infinite';
            
            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px); }
                    50% { transform: translateY(-10px); }
                }
                
                .feature {
                    transition: transform 0.3s ease;
                }
                
                .feature:hover {
                    transform: translateY(-5px);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
