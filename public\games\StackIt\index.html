<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StackIt - 3D Stacking Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .game-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .score {
            font-size: 2rem;
            font-weight: bold;
            color: #00d4ff;
        }

        .game-canvas {
            flex: 1;
            position: relative;
            background: linear-gradient(180deg, #0f3460 0%, #1a1a2e 100%);
        }

        .block {
            position: absolute;
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .block:hover {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .moving-block {
            animation: moveBlock 2s linear infinite;
        }

        @keyframes moveBlock {
            0% { left: 0%; }
            50% { left: calc(100% - 100px); }
            100% { left: 0%; }
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            z-index: 200;
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }

        .instructions {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 100;
        }

        .start-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 150;
        }

        .start-screen h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="score">Score: <span id="score">0</span></div>
            <div>Level: <span id="level">1</span></div>
        </div>

        <div class="game-canvas" id="gameCanvas">
            <!-- Blocks will be added here dynamically -->
        </div>

        <div class="instructions">
            <p>Click when the block is perfectly aligned to stack it!</p>
        </div>

        <div class="start-screen" id="startScreen">
            <h1>🎮 StackIt</h1>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Stack blocks as high as you can!</p>
            <button class="btn" onclick="startGame()">Start Game</button>
        </div>

        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button class="btn" onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <script>
        let score = 0;
        let level = 1;
        let gameRunning = false;
        let currentBlock = null;
        let stackedBlocks = [];
        let blockWidth = 100;
        let blockHeight = 30;
        let gameCanvas = document.getElementById('gameCanvas');
        let canvasHeight = window.innerHeight;
        let canvasWidth = window.innerWidth;

        function startGame() {
            document.getElementById('startScreen').style.display = 'none';
            gameRunning = true;
            score = 0;
            level = 1;
            stackedBlocks = [];
            blockWidth = 100;
            updateScore();

            // Add base block
            addBaseBlock();

            // Start first moving block
            setTimeout(() => {
                addMovingBlock();
            }, 500);
        }

        function addBaseBlock() {
            const baseBlock = document.createElement('div');
            baseBlock.className = 'block';
            baseBlock.style.width = blockWidth + 'px';
            baseBlock.style.height = blockHeight + 'px';
            baseBlock.style.left = (canvasWidth / 2 - blockWidth / 2) + 'px';
            baseBlock.style.bottom = '20px';
            baseBlock.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
            gameCanvas.appendChild(baseBlock);

            stackedBlocks.push({
                element: baseBlock,
                left: canvasWidth / 2 - blockWidth / 2,
                width: blockWidth,
                bottom: 20
            });
        }

        function addMovingBlock() {
            if (!gameRunning) return;

            currentBlock = document.createElement('div');
            currentBlock.className = 'block moving-block';
            currentBlock.style.width = blockWidth + 'px';
            currentBlock.style.height = blockHeight + 'px';
            currentBlock.style.bottom = (stackedBlocks.length * blockHeight + 20) + 'px';
            currentBlock.style.background = `linear-gradient(45deg, hsl(${level * 30}, 70%, 60%), hsl(${level * 30 + 30}, 70%, 50%))`;

            gameCanvas.appendChild(currentBlock);

            // Add click listener
            currentBlock.addEventListener('click', dropBlock);
            gameCanvas.addEventListener('click', dropBlock);
        }

        function dropBlock() {
            if (!currentBlock || !gameRunning) return;

            // Remove click listeners
            currentBlock.removeEventListener('click', dropBlock);
            gameCanvas.removeEventListener('click', dropBlock);

            // Stop animation
            currentBlock.classList.remove('moving-block');

            // Get current position
            const currentLeft = currentBlock.offsetLeft;
            const lastBlock = stackedBlocks[stackedBlocks.length - 1];

            // Calculate overlap
            const leftEdge = Math.max(currentLeft, lastBlock.left);
            const rightEdge = Math.min(currentLeft + blockWidth, lastBlock.left + lastBlock.width);
            const overlap = rightEdge - leftEdge;

            if (overlap <= 0) {
                // Game over
                gameOver();
                return;
            }

            // Update block size and position based on overlap
            const newWidth = overlap;
            const newLeft = leftEdge;

            currentBlock.style.width = newWidth + 'px';
            currentBlock.style.left = newLeft + 'px';

            // Add to stacked blocks
            stackedBlocks.push({
                element: currentBlock,
                left: newLeft,
                width: newWidth,
                bottom: stackedBlocks.length * blockHeight + 20
            });

            // Update game state
            score += 10;
            level = Math.floor(score / 100) + 1;
            blockWidth = newWidth;
            updateScore();

            // Check if block is too small
            if (newWidth < 20) {
                gameOver();
                return;
            }

            // Add next block
            currentBlock = null;
            setTimeout(() => {
                addMovingBlock();
            }, 800);
        }

        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';
        }

        function restartGame() {
            // Clear all blocks
            const blocks = document.querySelectorAll('.block');
            blocks.forEach(block => block.remove());

            // Reset game state
            stackedBlocks = [];
            currentBlock = null;
            blockWidth = 100;

            // Hide game over screen
            document.getElementById('gameOver').style.display = 'none';

            // Start new game
            startGame();
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
            document.getElementById('level').textContent = level;
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            canvasHeight = window.innerHeight;
            canvasWidth = window.innerWidth;
        });
    </script>
</body>
</html>
