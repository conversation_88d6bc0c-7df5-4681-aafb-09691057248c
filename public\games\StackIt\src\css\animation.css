/* animation: fadeinDown */
@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translate3d(0, -25%, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

/* animation: fadeInLeft */
@keyframes fadeInLeft {
	from {
		opacity: 0;
		transform: translate3d(-25%, 0, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

/* animation: fadeinDown */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translate3d(0, 25%, 0);
	}

	to {
		opacity: 1;
		transform: none;
	}
}

/* animation: popIn */
@keyframes popIn {
	from {
		transform: scale(0.5);
		opacity: 0;
	}

	80% {
		transform: scale(1.1);
		opacity: 1;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}