/* alias-form wrapper */
.alias-form {
  flex-grow: 3;
	position: relative;
	width: 100vw;
	display: flex;
	flex-direction: row;
	justify-content: center;
  animation-name: fadeInUp;
	animation-duration: 0.4s;
	animation-timing-function: ease-in-out;
}

.alias-form-input {
  width: 40vw;
  min-width: 25rem;
  height: 5rem;
  box-sizing: border-box;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0);
  border: 1px white solid;
  border-radius: 20px;
  font-size: 2.1rem;
}

.alias-form-input:focus {
  outline: none;
  border: 2px orange solid;
}

