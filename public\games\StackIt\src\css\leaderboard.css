/* scoreboard wrapper */
.scoreboard {
  position: relative;
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
	justify-content: flex-start;
	align-items: center;
  align-content: center;
  /* border: 1px white dashed; */
}

.scoreboard-list {
  width: 40vw;
  min-width: 25rem;
  list-style: none;
  display: inline-flex;
  flex-direction: column;
	justify-content: flex-start;
	align-items: center;
  align-content: center;
  padding: 1rem 0;
  border: 1px white solid;
  border-radius: 20px;
}

.scoreboard-list > li {
  font-size: 2.1rem;
  color: white;
}

.scoreboard-list > li:nth-child(even) {
  font-size: 5rem;
}

/* leaderboard wrapper */
.leaderboard {
  position: relative;
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
	justify-content: flex-start;
	align-items: center;
  align-content: center;
  /* border: 1px white dashed; */
}

.leaderboard-list {
  width: 40vw;
  min-width: 25rem;
  list-style: none;
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
	justify-content: flex-start;
	align-items: center;
  align-content: center;
  padding: 1rem 2rem;
  border: 1px white solid;
  border-radius: 20px;
}

.leaderboard-list > li:first-child {
  margin-bottom: 1rem;
  font-size: 2.1rem;
  color: white;
}

.leaderboard-item {
  display: inline-flex;
  flex-direction: row;
	justify-content: space-between;
	align-items: center;
  align-content: center;
  width: 100%;
}

.leaderboard-item > div {
  font-size: 2.1rem;
  color: white;
}
