.avatar {
  display: inline-flex;
  flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	align-content: center;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
  padding: 1rem;
  width: max-content;
  animation-name: fadeInDown;
	animation-duration: 0.4s;
	animation-timing-function: ease-in-out;
  /* border: 1px white dashed; */
}

.avatar-photo {
  border-radius: 30px;
  height: 60px;
  width: 60px;
  margin-bottom: 1rem;
}

.avatar-info {
  color: white;
  font-size: 2.1rem;
  background-color: rgba(255, 255, 255, 0);
  white-space: nowrap;
}