import React, { useRef, useState } from 'react';
import PianoTiles from './PianoTiles';

interface GameRendererProps {
  gameId: string;
  gameUrl?: string;
  gameTitle: string;
  isFullscreen?: boolean;
  onScoreUpdate?: (score: number) => void;
  onGameEnd?: (finalScore: number) => void;
  onLoad?: () => void;
}

const GameRenderer: React.FC<GameRendererProps> = ({
  gameId,
  gameUrl,
  gameTitle,
  isFullscreen = false,
  onScoreUpdate,
  onGameEnd,
  onLoad
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isGameLoaded, setIsGameLoaded] = useState(false);

  const handleIframeLoad = () => {
    setIsGameLoaded(true);
    onLoad?.();
  };

  // Render React component games
  const renderComponentGame = () => {
    switch (gameId) {
      case 'piano_tiles':
        return (
          <PianoTiles 
            onScoreUpdate={onScoreUpdate}
            onGameEnd={onGameEnd}
          />
        );
      default:
        return null;
    }
  };

  // Check if this is a component-based game
  const isComponentGame = ['piano_tiles'].includes(gameId) || gameUrl === 'COMPONENT';

  if (isComponentGame) {
    return (
      <div className={`w-full h-full ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
        {renderComponentGame()}
      </div>
    );
  }

  // Render iframe-based games
  return (
    <div className={`relative ${isFullscreen ? 'w-full h-full' : 'flex-1'}`}>
      {/* Loading indicator */}
      {!isGameLoaded && (
        <div className="absolute inset-0 bg-deep-space flex items-center justify-center z-20">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-accent-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-accent-cyan font-medium">
              Loading Game...
            </p>
            <p className="text-secondary-text text-sm mt-2">
              Click on the game area once loaded to start playing
            </p>
          </div>
        </div>
      )}

      {/* Game iframe */}
      <iframe
        ref={iframeRef}
        src={gameUrl}
        className={`w-full h-full border-0 bg-black ${
          isFullscreen ? 'object-cover' : ''
        }`}
        title={gameTitle}
        allow="fullscreen; gamepad; microphone; camera"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-pointer-lock"
        onLoad={handleIframeLoad}
        tabIndex={0}
        style={{
          ...(isFullscreen && {
            width: "100vw",
            height: "100vh",
            objectFit: "cover",
          }),
        }}
      />
    </div>
  );
};

export default GameRenderer;
