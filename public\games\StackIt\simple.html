<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StackIt - 3D Stacking Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .game-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .score {
            font-size: 2rem;
            font-weight: bold;
            color: #00d4ff;
        }

        .three-container {
            flex: 1;
            position: relative;
        }

        .instructions {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 1rem;
            border-radius: 10px;
        }

        .start-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 150;
        }

        .start-screen h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #9c27b0);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            z-index: 200;
            display: none;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="score">Score: <span id="score">0</span></div>
            <div>Perfect: <span id="perfect">0</span></div>
        </div>

        <div class="three-container" id="threeContainer">
            <!-- Three.js canvas will be added here -->
        </div>

        <div class="instructions">
            <p>Click or tap when the block is perfectly aligned!</p>
            <p>Build the tallest tower possible</p>
        </div>

        <div class="start-screen" id="startScreen">
            <h1>🎮 StackIt</h1>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">
                A 3D block stacking game built with Three.js
            </p>
            <p style="margin-bottom: 2rem;">
                Stack blocks as high as you can with perfect timing!
            </p>
            <button class="btn" onclick="startGame()">Start Game</button>
        </div>

        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <p>Perfect Stacks: <span id="finalPerfect">0</span></p>
            <button class="btn" onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <script>
        // Game variables
        let scene, camera, renderer;
        let stack = [];
        let overhangs = [];
        let boxHeight = 1;
        let originalBoxSize = 3;
        let gameStarted = false;
        let gameEnded = false;
        let score = 0;
        let perfectCount = 0;

        // Initialize Three.js
        function initThree() {
            // Scene
            scene = new THREE.Scene();

            // Camera
            const aspect = window.innerWidth / window.innerHeight;
            const width = 10;
            const height = width / aspect;
            camera = new THREE.OrthographicCamera(
                width / -2, width / 2, height / 2, height / -2, 0, 100
            );
            camera.position.set(4, 4, 4);
            camera.lookAt(0, 0, 0);

            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x1a1a2e);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            document.getElementById('threeContainer').appendChild(renderer.domElement);

            // Lighting
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
            directionalLight.position.set(10, 20, 0);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Add foundation
            addLayer(0, 0, originalBoxSize, originalBoxSize);

            // Add first layer
            addLayer(-10, 0, originalBoxSize, originalBoxSize, 'x');
        }

        function addLayer(x, z, width, depth, direction) {
            const y = boxHeight * stack.length;
            const layer = generateBox(x, y, z, width, depth, false);
            layer.direction = direction;
            stack.push(layer);
        }

        function generateBox(x, y, z, width, depth, falls) {
            // Geometry
            const geometry = new THREE.BoxGeometry(width, boxHeight, depth);
            
            // Material with random color
            const hue = (stack.length * 30) % 360;
            const color = new THREE.Color().setHSL(hue / 360, 0.7, 0.6);
            const material = new THREE.MeshLambertMaterial({ color });

            // Mesh
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(x, y, z);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            if (falls) {
                overhangs.push(mesh);
            } else {
                scene.add(mesh);
            }

            return {
                threejs: mesh,
                width,
                depth
            };
        }

        function startGame() {
            document.getElementById('startScreen').style.display = 'none';
            gameStarted = true;
            gameEnded = false;
            
            // Start animation
            animate();
            
            // Add click listener
            window.addEventListener('click', splitBlockAndAddNext);
            window.addEventListener('touchstart', splitBlockAndAddNext);
        }

        function splitBlockAndAddNext() {
            if (!gameStarted || gameEnded) return;

            const topLayer = stack[stack.length - 1];
            const previousLayer = stack[stack.length - 2];

            const direction = topLayer.direction;

            const size = direction == 'x' ? topLayer.width : topLayer.depth;
            const delta = topLayer.threejs.position[direction] - previousLayer.threejs.position[direction];
            const overhangSize = Math.abs(delta);
            const overlap = size - overhangSize;

            if (overlap > 0) {
                // Cut the box
                cutBox(topLayer, overlap, size, delta);

                // Overhang
                const overhangShift = (overlap / 2 + overhangSize / 2) * Math.sign(delta);
                const overhangX = direction == 'x' ? topLayer.threejs.position.x + overhangShift : topLayer.threejs.position.x;
                const overhangZ = direction == 'z' ? topLayer.threejs.position.z + overhangShift : topLayer.threejs.position.z;
                const overhangWidth = direction == 'x' ? overhangSize : topLayer.width;
                const overhangDepth = direction == 'z' ? overhangSize : topLayer.depth;

                addOverhang(overhangX, overhangZ, overhangWidth, overhangDepth);

                // Next layer
                const nextX = direction == 'x' ? topLayer.threejs.position.x : -10;
                const nextZ = direction == 'z' ? topLayer.threejs.position.z : -10;
                const newWidth = topLayer.width;
                const newDepth = topLayer.depth;
                const nextDirection = direction == 'x' ? 'z' : 'x';

                // Check for perfect alignment
                if (Math.abs(delta) < 0.05) {
                    perfectCount++;
                    updateScore();
                }

                score++;
                updateScore();

                addLayer(nextX, nextZ, newWidth, newDepth, nextDirection);
            } else {
                // Game over
                gameOver();
            }
        }

        function cutBox(topLayer, overlap, size, delta) {
            const direction = topLayer.direction;
            const newWidth = direction == 'x' ? overlap : topLayer.width;
            const newDepth = direction == 'z' ? overlap : topLayer.depth;

            // Update size
            topLayer.width = newWidth;
            topLayer.depth = newDepth;

            // Update geometry
            topLayer.threejs.scale[direction] = overlap / size;

            // Update position
            topLayer.threejs.position[direction] -= delta / 2;
        }

        function addOverhang(x, z, width, depth) {
            const y = boxHeight * (stack.length - 1);
            const overhang = generateBox(x, y, z, width, depth, true);
            
            // Add physics to overhang (simple fall)
            const fallSpeed = 0.1;
            const fallAnimation = () => {
                overhang.threejs.position.y -= fallSpeed;
                overhang.threejs.rotation.x += 0.05;
                overhang.threejs.rotation.z += 0.05;
                
                if (overhang.threejs.position.y > -20) {
                    requestAnimationFrame(fallAnimation);
                } else {
                    scene.remove(overhang.threejs);
                }
            };
            
            setTimeout(() => {
                scene.add(overhang.threejs);
                fallAnimation();
            }, 500);
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
            document.getElementById('perfect').textContent = perfectCount;
        }

        function gameOver() {
            gameEnded = true;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('finalPerfect').textContent = perfectCount;
            document.getElementById('gameOver').style.display = 'block';
            
            window.removeEventListener('click', splitBlockAndAddNext);
            window.removeEventListener('touchstart', splitBlockAndAddNext);
        }

        function restartGame() {
            // Reset game state
            score = 0;
            perfectCount = 0;
            stack = [];
            overhangs = [];
            gameStarted = false;
            gameEnded = false;

            // Clear scene
            while(scene.children.length > 0) {
                scene.remove(scene.children[0]);
            }

            // Re-add lighting
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
            directionalLight.position.set(10, 20, 0);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            // Reset foundation and first layer
            addLayer(0, 0, originalBoxSize, originalBoxSize);
            addLayer(-10, 0, originalBoxSize, originalBoxSize, 'x');

            // Hide game over screen
            document.getElementById('gameOver').style.display = 'none';
            
            // Update UI
            updateScore();
            
            // Start game
            startGame();
        }

        function animate() {
            if (!gameStarted || gameEnded) return;

            const speed = 0.15;
            const topLayer = stack[stack.length - 1];
            
            if (topLayer.direction == 'x') {
                topLayer.threejs.position.x = Math.sin(Date.now() / 1000) * 5;
            } else {
                topLayer.threejs.position.z = Math.sin(Date.now() / 1000) * 5;
            }

            // Update camera to follow the stack
            if (stack.length > 5) {
                camera.position.y = boxHeight * (stack.length - 2) + 4;
                camera.lookAt(0, boxHeight * (stack.length - 2), 0);
            }

            renderer.render(scene, camera);
            requestAnimationFrame(animate);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            const aspect = window.innerWidth / window.innerHeight;
            const width = 10;
            const height = width / aspect;
            
            camera.left = width / -2;
            camera.right = width / 2;
            camera.top = height / 2;
            camera.bottom = height / -2;
            camera.updateProjectionMatrix();
            
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Initialize the game
        initThree();
    </script>
</body>
</html>
