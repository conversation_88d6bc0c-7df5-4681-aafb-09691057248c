{"name": "stackit_react", "version": "1.0.0", "description": "A WebGL game built on THREE.js", "main": "index.js", "scripts": {"start": "webpack-dev-server --mode development --open --hot --https", "build": "webpack --mode production", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/YifuChen/StackIt.git"}, "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.2.3", "@babel/preset-env": "^7.2.3", "@babel/preset-react": "^7.0.0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.4", "css-loader": "^2.1.0", "dotenv-webpack": "^1.6.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-loader": "^2.1.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-react": "^7.12.0", "file-loader": "^3.0.1", "html-webpack-plugin": "^3.2.0", "jest": "^23.6.0", "style-loader": "^0.23.1", "webpack": "^4.28.3", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.14", "worker-loader": "^2.0.0"}, "dependencies": {"redux-devtools-extension": "^2.13.7", "cross-fetch": "^3.0.0", "firebase": "^5.7.2", "physijs-webpack": "github:agilgur5/physijs-webpack", "prop-types": "^15.6.2", "react": "^16.7.0", "react-dom": "^16.7.0", "react-redux": "^6.0.0", "redux": "^4.0.1", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "three": "^0.99.0"}}