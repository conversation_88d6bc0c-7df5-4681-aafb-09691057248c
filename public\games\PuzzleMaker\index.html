<!DOCTYPE html>
<html>
<head>
<title>Fifteen puzzle maker</title>
<link rel="shortcut icon" href="icon.jpg" type="image/x-icon">
<meta content="#c4cebb" name="theme-color" charset="UTF-8">
<meta name="description" content="A simple implementation of the classic mini-game Fifteen Sliding Puzzle, using HTML5 DOM document elements and without using Canvas or third party libraries.">
<meta name="keywords" content="15Puzzle, js, Open Source, HTML5, Sliding, web, base">
<meta name="author" content="Kirill Live">
<style>
body {
    height: 100vh;
    padding: 0;
    display: grid;
    align-content: center;
    justify-content: center;background-color:#dfebd5;
}
*{
	font-family:Arial;
    font-size:12px;
	border-collapse:collapse;
	border:none;
	margin:0;
	padding:0;
	border-spacing:0px;
	-webkit-touch-callout:none;
	-webkit-user-select:none;
	-khtml-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	user-select:none;
	outline:none;
}
.input_text{
	border:0;
	font-family:Arial;
	font-size:12px;
	-webkit-appearance:none;
	border-bottom:1px solid #c4cebb;
	background-color:transparent;
	cursor:text;
	height:28px;
	width:100%;
	-webkit-touch-callout:text;-webkit-user-select:text;-khtml-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;
}
.button{border-radius:8px;width:100%;height:32px;background-color:#c4cebb;display:grid;align-content: center;justify-content: center;cursor:pointer;}
.button:hover{background-color:#dfebd5;}
.switch {
  position:relative;
  display:inline-block;
  width:50px;
  height:24px;
}
.switch input{opacity:0;width:0;height:0;}
.slider{
  position:absolute;
  cursor:pointer;
  top:0;
  left:0;
  right:0;
  bottom:0;
  background-color:#c4cebb;
  transition:.2s;
  border-radius:12px;
}
.slider:before{
  position:absolute;
  content:"";
  height:18px;
  width:18px;
  left:3px;
  bottom:3px;
  background-color:#fff;
  transition:.2s;
  border-radius:12px;
}
input:checked+.slider{background-color:#dfebd5;}
input:checked+.slider:before{transform:translateX(26px);}
</style>
</head>
<body>
<div id='fifteen'></div> <!--element "fifteen" in which the game will take place-->
<div style='position:fixed;top:0;left:0;box-shadow:0px 2px 24px rgba(110,95,165,0.25);border-radius:0 0 16px 0;background-color:#fff;width:196px;'>
    <div style="display:none;position:fixed;"><img id="art" src=""/><a id="dwonload"></a><input id="img_file" type="file" accept="image/png,image/gif,image/jpeg,image/webp"/></div>
    <div style="overflow:auto;width:180px;max-height:100vh;height:100%;padding:8px;">
        <table style="border-collapse:collapse;" border="1">
        <tbody>
        <tr>
        <td style="height:48px;" colspan="2"><div class="button" onclick="img_file.click();">Load img file</div></td>
        </tr>
        <tr>
        <td style="width:68px;height:34px;padding-right:8px;" align="right">Difficulty</td>
        <td><input onchange="setup.puzzle_fifteen.diff=this.value;fifteen_update();" value="300" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Width</td>
        <td><input id="width" onchange="setup.puzzle_fifteen.size[0]=parseInt(this.value);fifteen_update();" value="1024" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Height</td>
        <td><input id="height" onchange="setup.puzzle_fifteen.size[1]=parseInt(this.value);fifteen_update();" value="1281" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Image fill</td>
        <td align="center"><label class="switch">
            <input type="checkbox" onchange="setup.puzzle_fifteen.art.ratio=this.checked;fifteen_update();">
            <span class="slider round"></span>
        </label></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Grid width</td>
        <td><input id="grid_width" onchange="setup.puzzle_fifteen.grid[0]=parseInt(this.value);fifteen_update();" value="3" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Grid height</td>
        <td><input id="grid_height" onchange="setup.puzzle_fifteen.grid[1]=parseInt(this.value);fifteen_update();" value="4" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Move time</td>
        <td ><input onchange="setup.puzzle_fifteen.time=this.value;fifteen_update();" value="0.4" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Numbers</td>
        <td align="center"><label class="switch">
            <input type="checkbox" onchange="setup.puzzle_fifteen.number=this.checked;fifteen_update();">
            <span class="slider round"></span>
        </label></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Puzzle fill</td>
        <td align="center"><label class="switch">
            <input type="checkbox" onchange="setup.puzzle_fifteen.fill=this.checked;if(!this.checked){window.removeEventListener('resize',fifteen_resize,true);f.style.transform='scale(1)'};fifteen_update();" checked>
            <span class="slider round"></span>
        </label></td>
        </tr>
        <tr>
        <td style="height:34px;padding-right:8px;" align="right">Style blocks</td>
        <td><input id="slot_style" onchange="setup.puzzle_fifteen.style=this.value;fifteen_update();" class="input_text" type="text"></td>
        </tr>
        <tr>
        <td style="height:48px;" colspan="2"><div class="button" onclick="fifteen_update();">Refresh</div></td>
        </tr>
        <tr>
        <td style="height:48px;" colspan="2"><div class="button" onclick="fifteen_build();">Create HTML game</div></td>
        </tr>
        <tr><td style="height:16px;" colspan="2"></td></tr>
        </tbody>
        </table>
    </div>
</div>
<script>
var setup={
     puzzle_fifteen:{
        diff:300, // number of movements of the slots for shuffling pictures
        size:[512,640], // element size "fifteen" in pixels only
        grid:[3,4], // the number of squares in the height and width of the picture
        fill:true, // Stretching the area with the game to fit the element is recommended for fullscreen
        number:false, // Slot sequence number
        art:{
            url:"art.jpg", // path to the picture (you can use any format of supported browsers, gif-animation of svg)
            ratio:false // enlarge the picture in height or width
        },
        // optional elements
        keyBoard:true, // Control using the keys on the keyboard
        gamePad:true, // Control using the joystick on the Gamepad
        time:0.1, // block move animation time
        style:"background-color:#c4cebb;display:grid;justify-items:center;align-items:center;font-family:Arial;color:#fff;border-radius:12px;font-size:32px;" // style for puzzle square
     }
}
slot_style.value=setup.puzzle_fifteen.style;
var img_file=document.getElementById('img_file'),img=document.getElementById("art"),file;
img_file.addEventListener('change',loadFiles);
function loadFiles(e){
    file=img_file.files[0];
    adden_file();
}
function adden_file(){
    setup.puzzle_fifteen.art.url=window.URL.createObjectURL(file)
    img.src=setup.puzzle_fifteen.art.url;
    img.onload=function(){setup.puzzle_fifteen.size=[img.width,img.height];auto_grid();auto_style();fifteen_update();}
}
function auto_grid(){
    let s=setup.puzzle_fifteen
    if(s.size[1]<s.size[0]){s.grid=[Math.round(s.size[0]/(s.size[1]/4))-1,3]}
    else{s.grid=[3,Math.round(s.size[1]/(s.size[0]/4))-1]}
    grid_width.value=s.grid[0];
    grid_height.value=s.grid[1];
    width.value=s.size[0];
    height.value=s.size[1];
}
function auto_style(){
    let s=setup.puzzle_fifteen,v,i;
    if(s.size[1]<s.size[0]){v=Math.round((s.size[0]/s.grid[0])/16)}
    else{v=Math.round((s.size[1]/s.grid[1])/16)}
    d=s.style.split(";");
    for(i=0;i<d.length;i++){
        if(d[i].includes("border-radius")){s.style=s.style.replace(d[i],"border-radius:"+Math.round(v*1.5)+"px")}
        else if(d[i].includes("font-size")){s.style=s.style.replace(d[i],"font-size:"+(v*3)+"px")}
    }slot_style.value=s.style;
}
function fifteen_update(){
    f.innerHTML="";
    ceation_slots();
}
function fifteen_build(){
    let reader=new FileReader();
    if(file){reader.readAsDataURL(file);}else{alert('Please upload a file with a picture')}
    reader.onload=function(){setup.puzzle_fifteen.art.url=reader.result;gen_file();}
    reader.onerror=function(error){alert('Error: '+error);}
    function gen_file(){
        let url="fifteen_puzzle.js";
        let xmlhttp=new XMLHttpRequest();
        xmlhttp.onreadystatechange=function(){
            if(this.readyState==4&&this.status==200){
                let html="data:text/json;charset=utf-8,"+encodeURIComponent("<!DOCTYPE html>\n<html>\n<head>\n<style>\n body{height:97vh;padding:0;display:grid;align-content:center;justify-content:center;}\n</style>\n</head>\n<body>\n<div id='fifteen'></div>\n<script>\n"+this.responseText.replace("setup.puzzle_fifteen",JSON.stringify(setup.puzzle_fifteen,null,'\t'))+"\n<\/script>\n<\/body>\n<\/html>");
                let a=document.getElementById('dwonload');
                a.setAttribute("href", html );
                a.setAttribute("download","index.html");
                a.click();
            }
        };
        xmlhttp.open("GET",url,true);
        xmlhttp.send();
        xmlhttp.onerror=function(){if(this.status==0){alert('runetime not loaded');}}
    }
}
var drop = {
    init:function(){
        if (window.File&&window.FileReader&&window.FileList&&window.Blob) {
            window.addEventListener("dragover",function(e){
                e.preventDefault();
                e.stopPropagation();
            });
            window.addEventListener("drop",function(e){
                e.preventDefault();
                e.stopPropagation();
                file=e.dataTransfer.files[0];
                adden_file();
            });
        }
    },
};
window.addEventListener("DOMContentLoaded", drop.init);
</script>
<script src="fifteen_puzzle.js"></script> <!--path to file engine-->
</body>
</html>
